import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient

from app.main import app
from app.database import Base, get_db
from app.models import Goal as GoalModel

# Test database
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

@pytest.fixture
def test_db():
    Base.metadata.create_all(bind=engine)
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()
        Base.metadata.drop_all(bind=engine)

@pytest.fixture
def client(test_db):
    def override_get_db():
        try:
            yield test_db
        finally:
            test_db.close()
    
    app.dependency_overrides[get_db] = override_get_db
    yield TestClient(app)
    app.dependency_overrides.clear()

def test_create_goal(client):
    response = client.post(
        "/api/goals",
        json={
            "name": "Test Goal",
            "description": "Test Description",
            "status": "ACTIVE"
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["data"]["name"] == "Test Goal"

def test_get_goals(client, test_db):
    # Create test goal
    goal = GoalModel(name="Test Goal", description="Test", status="ACTIVE")
    test_db.add(goal)
    test_db.commit()
    
    response = client.get("/api/goals")
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert len(data["data"]) == 1
    assert data["data"][0]["name"] == "Test Goal"

def test_get_goal_by_id(client, test_db):
    goal = GoalModel(name="Test Goal", description="Test", status="ACTIVE")
    test_db.add(goal)
    test_db.commit()
    test_db.refresh(goal)
    
    response = client.get(f"/api/goals/{goal.id}")
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["data"]["id"] == goal.id

def test_update_goal(client, test_db):
    goal = GoalModel(name="Old Name", description="Old Desc", status="ACTIVE")
    test_db.add(goal)
    test_db.commit()
    test_db.refresh(goal)
    
    response = client.put(
        f"/api/goals/{goal.id}",
        json={
            "name": "Updated Name",
            "status": "COMPLETED"
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["data"]["name"] == "Updated Name"
    assert data["data"]["status"] == "COMPLETED"

def test_delete_goal(client, test_db):
    goal = GoalModel(name="Test Goal", description="Test", status="ACTIVE")
    test_db.add(goal)
    test_db.commit()
    test_db.refresh(goal)
    
    response = client.delete(f"/api/goals/{goal.id}")
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    
    # Verify deletion
    response = client.get(f"/api/goals/{goal.id}")
    assert response.status_code == 404