from sqlalchemy import Column, Integer, String, DateTime, Text, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, JSO<PERSON>
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
from enum import Enum

Base = declarative_base()

class GoalStatus(str, Enum):
    ACTIVE = "ACTIVE"
    COMPLETED = "COMPLETED"
    PAUSED = "PAUSED"
    CANCELLED = "CANCELLED"

class TaskStatus(str, Enum):
    ACTIVE = "ACTIVE"
    COMPLETED = "COMPLETED"
    PAUSED = "PAUSED"
    CANCELLED = "CANCELLED"

class ExecutionStatus(str, Enum):
    TRIGGERED = "TRIGGERED"
    STARTED = "STARTED"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    DEFERRED = "DEFERRED"
    IGNORED = "IGNORED"

class InsightType(str, Enum):
    WEEKLY_REPORT = "weekly_report"
    PATTERN_ANALYSIS = "pattern_analysis"
    PERFORMANCE = "performance"
    MOTIVATION = "motivation"
    ADJUSTMENT = "adjustment"

class Goal(Base):
    __tablename__ = "goals"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    description = Column(Text)
    status = Column(String, default=GoalStatus.ACTIVE, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    subtasks = relationship("SubTask", back_populates="goal", cascade="all, delete-orphan")

class SubTask(Base):
    __tablename__ = "subtasks"
    
    id = Column(Integer, primary_key=True, index=True)
    goal_id = Column(Integer, ForeignKey("goals.id"), nullable=False)
    name = Column(String, nullable=False)
    description = Column(Text)
    priority = Column(Integer, default=-1)
    trigger_frequency = Column(String, nullable=False)
    trigger_time = Column(String, nullable=False)
    estimated_duration = Column(Integer, default=30)
    status = Column(String, default=TaskStatus.ACTIVE, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    goal = relationship("Goal", back_populates="subtasks")
    execution_logs = relationship("ExecutionLog", back_populates="subtask", cascade="all, delete-orphan")

class ExecutionLog(Base):
    __tablename__ = "execution_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    subtask_id = Column(Integer, ForeignKey("subtasks.id"), nullable=False)
    triggered_at = Column(DateTime, default=datetime.utcnow)
    status = Column(String, nullable=False)
    failure_reason_tag = Column(String)
    user_feedback = Column(Text)
    completed_at = Column(DateTime)
    
    subtask = relationship("SubTask", back_populates="execution_logs")

class Insight(Base):
    __tablename__ = "insights"
    
    id = Column(Integer, primary_key=True, index=True)
    type = Column(String, nullable=False)
    content = Column(Text, nullable=False)
    data = Column(JSON)
    created_at = Column(DateTime, default=datetime.utcnow)
    applied = Column(Boolean, default=False)

class AgentState(Base):
    __tablename__ = "agent_states"
    
    id = Column(String, primary_key=True)
    state_data = Column(JSON)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)