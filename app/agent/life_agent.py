from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import json
import logging
from sqlalchemy.orm import Session
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, END
from app.config import settings
from app.agent.state import <PERSON>Contex<PERSON>, AgentState, ScheduledAction
from app.models import Goal as GoalModel, SubTask as SubTaskModel, ExecutionLog as ExecutionLogModel, Insight as InsightModel
from app.models import GoalStatus, TaskStatus, ExecutionStatus

logger = logging.getLogger(__name__)

class LifeAgent:
    def __init__(self, db: Session):
        self.db = db
        self.llm = ChatOpenAI(
            api_key=settings.openai_api_key,
            base_url=settings.openai_base_url,
            model=settings.model,
            temperature=settings.temperature,
            max_tokens=settings.max_tokens
        )
        self.graph = self._create_graph()
        
    def _create_graph(self):
        workflow = StateGraph(AgentContext)
        
        workflow.add_node("plan", self._plan_node)
        workflow.add_node("schedule", self._schedule_node)
        workflow.add_node("act", self._act_node)
        workflow.add_node("reflect", self._reflect_node)
        workflow.add_node("adjust", self._adjust_node)
        workflow.add_node("wait", self._wait_node)
        workflow.add_node("terminate", self._terminate_node)
        
        workflow.set_entry_point("plan")
        
        workflow.add_edge("plan", "schedule")
        workflow.add_conditional_edges(
            "schedule",
            self._after_schedule_decision,
            {
                "act": "act",
                "wait": "wait",
                "reflect": "reflect",
                "terminate": "terminate",
                "schedule": "schedule"
            }
        )
        
        workflow.add_edge("act", "schedule")
        workflow.add_edge("wait", "schedule")
        workflow.add_edge("reflect", "adjust")
        workflow.add_edge("adjust", "schedule")
        
        return workflow.compile()
    
    def _plan_node(self, state: AgentContext) -> Dict[str, Any]:
        logger.info(f"=== Planning Phase (iteration #{state.iteration_count}) ===")
        
        try:
            # Get the current goal
            goal = self.db.query(GoalModel).filter(
                GoalModel.status == GoalStatus.ACTIVE.value
            ).first()
            
            if not goal:
                return {
                    "current_state": AgentState.TERMINATED,
                    "termination_reason": "No active goals found"
                }
            
            # Analyze historical performance
            historical_data = self._analyze_historical_performance()
            
            # Generate intelligent plan using LLM
            plan_result = self._generate_intelligent_plan(goal, historical_data)
            
            # Create or update subtasks based on intelligent plan
            created_tasks = []
            for i, task_data in enumerate(plan_result.get("tasks", [])):
                # Check if task already exists
                existing_task = self.db.query(SubTaskModel).filter(
                    SubTaskModel.goal_id == goal.id,
                    SubTaskModel.name == task_data["name"]
                ).first()
                
                if not existing_task:
                    # Create new subtask from intelligent plan
                    new_task = SubTaskModel(
                        goal_id=goal.id,
                        name=task_data["name"],
                        description=task_data.get("description", ""),
                        estimated_duration=task_data.get("duration", 30),
                        status=TaskStatus.ACTIVE,
                        priority=i + 1
                    )
                    self.db.add(new_task)
                    self.db.commit()
                    created_tasks.append(new_task)
                else:
                    created_tasks.append(existing_task)
            
            # Create scheduled actions based on intelligent plan tasks
            now = datetime.now()
            actions = []
            
            # Use the tasks we just created/updated
            tasks_to_schedule = created_tasks if created_tasks else self.db.query(SubTaskModel).filter(
                SubTaskModel.goal_id == goal.id,
                SubTaskModel.status == TaskStatus.ACTIVE
            ).all()
            
            for i, task in enumerate(tasks_to_schedule):
                # Use optimal timing from intelligent plan if available
                optimal_hour = plan_result.get("tasks", [{}])[i].get("optimal_hour", 9) if i < len(plan_result.get("tasks", [])) else 9
                
                # Schedule based on optimal hour or sequential timing
                if i == 0:
                    push_time = now + timedelta(minutes=2)
                else:
                    hour_diff = optimal_hour - now.hour
                    if hour_diff <= 0:
                        hour_diff = i + 1
                    push_time = now + timedelta(hours=hour_diff)
                
                actions.append(ScheduledAction(
                    id=f"push-task-{task.id}",
                    action_type="push_reminder",
                    parameters={
                        "task_id": task.id,
                        "task_name": task.name,
                        "task_description": task.description,
                        "estimated_duration": task.estimated_duration,
                        "goal_name": goal.name,
                        "strategy": plan_result.get("strategy", "Intelligent scheduling")
                    },
                    scheduled_time=push_time
                ))
                
                # Add progress check
                check_time = push_time + timedelta(minutes=5)
                actions.append(ScheduledAction(
                    id=f"check-progress-{task.id}",
                    action_type="check_progress",
                    parameters={
                        "task_id": task.id,
                        "task_name": task.name
                    },
                    scheduled_time=check_time
                ))
            
            # Add reflection based on plan strategy
            if actions:
                reflection_time = now + timedelta(minutes=15)
                actions.append(ScheduledAction(
                    id="weekly-reflection",
                    action_type="reflect",
                    parameters={
                        "reflection_type": "weekly_review",
                        "plan_strategy": plan_result.get("strategy", "Data-driven")
                    },
                    scheduled_time=reflection_time
                ))
            
            return {
                "current_state": AgentState.SCHEDULING,
                "current_goal_id": goal.id,
                "scheduled_actions": actions,
                "iteration_count": state.iteration_count + 1
            }
            
        except Exception as e:
            logger.error(f"Planning phase error: {e}")
            return {
                "current_state": AgentState.SCHEDULING,
                "iteration_count": state.iteration_count + 1
            }
    
    def _schedule_node(self, state: AgentContext) -> Dict[str, Any]:
        logger.info("=== Scheduling Phase ===")
        
        now = datetime.now()
        ready_actions = [a for a in state.scheduled_actions 
                        if not a.executed and a.scheduled_time <= now]
        future_actions = [a for a in state.scheduled_actions 
                         if not a.executed and a.scheduled_time > now]
        
        logger.info(f"Total scheduled actions: {len(state.scheduled_actions)}")
        logger.info(f"Ready actions: {len(ready_actions)}")
        logger.info(f"Future actions: {len(future_actions)}")
        
        if ready_actions:
            logger.info(f"Found {len(ready_actions)} ready actions")
            return {"current_state": AgentState.ACTING}
        elif future_actions:
            next_action_time = min(a.scheduled_time for a in future_actions)
            wait_seconds = (next_action_time - now).total_seconds()
            
            # Allow waiting for initial actions even if they're scheduled soon
            if wait_seconds > 300 and len(state.scheduled_actions) > 0:  # 5 minutes
                return {
                    "current_state": AgentState.TERMINATED,
                    "termination_reason": f"Next action in {wait_seconds/3600:.1f} hours"
                }
            
            logger.info(f"Waiting {wait_seconds:.1f} seconds for next action")
            return {
                "current_state": AgentState.WAITING,
                "wait_until": next_action_time
            }
        else:
            return {
                "current_state": AgentState.TERMINATED,
                "termination_reason": "All scheduled actions completed"
            }
    
    def _act_node(self, state: AgentContext) -> Dict[str, Any]:
        logger.info("=== Acting Phase ===")
        
        now = datetime.now()
        ready_actions = [a for a in state.scheduled_actions 
                        if not a.executed and a.scheduled_time <= now]
        
        completed_actions = []
        push_history = list(state.push_history)
        
        for action in ready_actions:
            logger.info(f"Executing action: {action.id} ({action.action_type})")
            
            action.executed = True
            action.result = {
                "status": "executed",
                "executed_at": now.isoformat(),
                "action_type": action.action_type
            }
            
            if action.action_type == "push_reminder":
                task_id = action.parameters.get("task_id")
                if task_id:
                    # Create execution log
                    log = ExecutionLogModel(
                        subtask_id=task_id,
                        status=ExecutionStatus.TRIGGERED
                    )
                    self.db.add(log)
                    self.db.commit()
                    
                    push_history.append({
                        "action_id": action.id,
                        "task_id": task_id,
                        "type": "push_reminder",
                        "timestamp": now.isoformat()
                    })
            
            completed_actions.append(action)
        
        self.db.commit()
        
        remaining_actions = [a for a in state.scheduled_actions if not a.executed]
        updated_actions = remaining_actions + completed_actions
        
        return {
            "current_state": AgentState.SCHEDULING,
            "scheduled_actions": updated_actions,
            "completed_actions": state.completed_actions + completed_actions,
            "push_history": push_history
        }
    
    def _reflect_node(self, state: AgentContext) -> Dict[str, Any]:
        logger.info("=== Reflecting Phase ===")
        
        if not state.completed_actions:
            return {
                "current_state": AgentState.TERMINATED,
                "termination_reason": "No completed actions to reflect on"
            }
        
        # Analyze recent execution logs
        logs = self.db.query(ExecutionLogModel).filter(
            ExecutionLogModel.triggered_at >= datetime.now() - timedelta(days=7)
        ).all()
        
        analysis = self._analyze_execution_patterns(logs)
        
        # Create insight
        insight = InsightModel(
            type="weekly_report",
            content=f"Weekly reflection: {analysis['summary']}",
            data=analysis
        )
        self.db.add(insight)
        self.db.commit()
        
        return {
            "current_state": AgentState.ADJUSTING,
            "reflections": state.reflections + [insight.content]
        }
    
    def _adjust_node(self, state: AgentContext) -> Dict[str, Any]:
        logger.info("=== Adjusting Phase ===")
        
        # Check if adjustments are needed based on reflection
        if state.reflections and "need adjustment" in str(state.reflections[-1]).lower():
            logger.info("Adjustments needed, returning to planning")
            return {"current_state": AgentState.PLANNING}
        
        return {"current_state": AgentState.SCHEDULING}
    
    def _wait_node(self, state: AgentContext) -> Dict[str, Any]:
        logger.info("=== Waiting Phase ===")
        
        if state.wait_until:
            wait_time = (state.wait_until - datetime.now()).total_seconds()
            if wait_time > 0:
                logger.info(f"Waiting for {wait_time:.1f} seconds")
        
        return {"current_state": AgentState.SCHEDULING}
    
    def _terminate_node(self, state: AgentContext) -> Dict[str, Any]:
        logger.info("=== Terminating Phase ===")
        logger.info(f"Termination reason: {state.termination_reason}")
        return {"current_state": AgentState.TERMINATED}
    
    def _after_schedule_decision(self, state: AgentContext) -> str:
        if state.current_state == AgentState.ACTING:
            return "act"
        elif state.current_state == AgentState.WAITING:
            return "wait"
        elif state.current_state == AgentState.REFLECTING:
            return "reflect"
        elif state.current_state == AgentState.TERMINATED:
            return "terminate"
        return "schedule"
    
    def _analyze_historical_performance(self) -> Dict[str, Any]:
        logs = self.db.query(ExecutionLogModel).filter(
            ExecutionLogModel.triggered_at >= datetime.now() - timedelta(days=30)
        ).all()
        
        if not logs:
            return {"success_rate": 0, "summary": "No historical data"}
        
        total = len(logs)
        successful = len([log for log in logs if str(log.status) == str(ExecutionStatus.COMPLETED)])
        success_rate = (successful / total) * 100
        
        return {
            "success_rate": success_rate,
            "total_logs": total,
            "successful_logs": successful,
            "summary": f"Historical success rate: {success_rate:.1f}%"
        }
    
    def _generate_intelligent_plan(self, goal: GoalModel, historical_data: Dict[str, Any]) -> Dict[str, Any]:
        try:
            system_prompt = """You are an expert life coach. Create an intelligent plan for this goal:
1. Break down into specific, actionable tasks
2. Optimize timing based on historical data
3. Create motivating messages
4. Ensure realistic scheduling"""
            
            user_prompt = f"""
Goal: {goal.name}
Description: {goal.description}
Historical data: {json.dumps(historical_data, indent=2)}

Generate 3-5 specific tasks with optimal timing.
"""
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            response = self.llm.invoke(messages)
            
            # Parse response - simplified for demo
            # In production, parse the actual response content
            _ = response  # Use the response to avoid unused variable warning
            
            return {
                "goal_summary": goal.name,
                "tasks": [
                    {
                        "name": "Start task",
                        "description": "Begin working on the goal",
                        "duration": 30,
                        "optimal_hour": 9
                    }
                ],
                "strategy": "Data-driven scheduling"
            }
            
        except Exception as e:
            logger.error(f"LLM planning failed: {e}")
            return {"goal_summary": goal.name, "tasks": [], "strategy": "Fallback plan"}
    
    def _analyze_execution_patterns(self, logs) -> Dict[str, Any]:
        if not logs:
            return {"success_rate": 0, "summary": "No execution data"}
        
        total = len(logs)
        successful = len([log for log in logs if log.status == ExecutionStatus.COMPLETED])
        failed = len([log for log in logs if log.status in [ExecutionStatus.FAILED, ExecutionStatus.IGNORED]])
        
        success_rate = (successful / total) * 100 if total > 0 else 0
        
        # Count failure reasons
        failure_reasons = {}
        for log in logs:
            if log.failure_reason_tag:
                failure_reasons[log.failure_reason_tag] = failure_reasons.get(log.failure_reason_tag, 0) + 1
        
        return {
            "success_rate": success_rate,
            "failed_count": failed,
            "most_common_failure": max(failure_reasons.items(), key=lambda x: x[1])[0] if failure_reasons else None,
            "summary": f"Success rate: {success_rate:.1f}%, {failed} failures"
        }
    
    async def run_workflow(self) -> Dict[str, Any]:
        """Run the complete agent workflow"""
        context = AgentContext()
        max_iterations = 10
        
        while context.iteration_count < max_iterations and context.current_state != AgentState.TERMINATED:
            try:
                result = self.graph.invoke(context)
                
                # Update context
                for key, value in result.items():
                    if hasattr(context, key):
                        setattr(context, key, value)
                
                if context.current_state == AgentState.TERMINATED:
                    break
                    
            except Exception as e:
                logger.error(f"Workflow error: {e}")
                break
        
        return context.model_dump()
    
    def trigger_task_execution(self, task_id: int, action: str, feedback: Optional[str] = None) -> Dict[str, Any]:
        """Trigger manual task execution"""
        try:
            task = self.db.query(SubTaskModel).filter(SubTaskModel.id == task_id).first()
            if not task:
                return {"success": False, "message": "Task not found"}
            
            # Create execution log
            log = ExecutionLogModel(
                subtask_id=task_id,
                status=action.upper(),
                user_feedback=feedback
            )
            self.db.add(log)
            self.db.commit()
            
            return {
                "success": True,
                "message": f"Task {action} successfully",
                "log_id": log.id
            }
            
        except Exception as e:
            logger.error(f"Task execution error: {e}")
            return {"success": False, "message": str(e)}