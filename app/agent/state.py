from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional
from datetime import datetime
from enum import Enum

class AgentState(str, Enum):
    PLANNING = "planning"
    SCHEDULING = "scheduling"
    ACTING = "acting"
    REACTING = "reacting"
    REFLECTING = "reflecting"
    ADJUSTING = "adjusting"
    WAITING = "waiting"
    TERMINATED = "terminated"

class ScheduledAction(BaseModel):
    id: str
    action_type: str  # "push_reminder", "check_progress", "reflect", "adjust_plan"
    parameters: Dict[str, Any]
    scheduled_time: datetime
    executed: bool = False
    result: Optional[Dict[str, Any]] = None
    human_response: Optional[str] = None

class AgentContext(BaseModel):
    current_state: AgentState = AgentState.PLANNING
    current_goal_id: Optional[int] = None
    scheduled_actions: List[ScheduledAction] = []
    completed_actions: List[ScheduledAction] = []
    external_context: Dict[str, Any] = Field(default_factory=dict)
    reflections: List[str] = []
    iteration_count: int = 0
    termination_reason: Optional[str] = None
    wait_until: Optional[datetime] = None
    push_history: List[Dict[str, Any]] = Field(default_factory=list)

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }