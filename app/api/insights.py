from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from app.database import get_db
from app.models import Insight as InsightModel
from app.schemas import Insight, InsightCreate, ApiResponse, InsightType

router = APIRouter(prefix="/api/insights", tags=["insights"])

@router.get("/", response_model=ApiResponse)
async def get_insights(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    insight_type: Optional[InsightType] = None,
    applied: Optional[bool] = None,
    db: Session = Depends(get_db)
):
    """Get all insights with optional filtering"""
    try:
        query = db.query(InsightModel)
        
        if insight_type:
            query = query.filter(InsightModel.type == insight_type)
        
        if applied is not None:
            query = query.filter(InsightModel.applied == applied)
        
        insights = query.order_by(InsightModel.created_at.desc()).offset(skip).limit(limit).all()
        
        return ApiResponse(success=True, data=insights)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/", response_model=ApiResponse)
async def create_insight(
    insight: InsightCreate,
    db: Session = Depends(get_db)
):
    """Create a new insight"""
    try:
        db_insight = InsightModel(
            type=insight.type,
            content=insight.content,
            data=insight.data
        )
        db.add(db_insight)
        db.commit()
        db.refresh(db_insight)
        
        return ApiResponse(success=True, data=db_insight)
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{insight_id}", response_model=ApiResponse)
async def get_insight(insight_id: int, db: Session = Depends(get_db)):
    """Get a specific insight by ID"""
    try:
        insight = db.query(InsightModel).filter(InsightModel.id == insight_id).first()
        if not insight:
            raise HTTPException(status_code=404, detail="Insight not found")
        
        return ApiResponse(success=True, data=insight)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/{insight_id}/apply", response_model=ApiResponse)
async def apply_insight(insight_id: int, db: Session = Depends(get_db)):
    """Mark an insight as applied"""
    try:
        insight = db.query(InsightModel).filter(InsightModel.id == insight_id).first()
        if not insight:
            raise HTTPException(status_code=404, detail="Insight not found")
        
        insight.applied = True
        db.commit()
        db.refresh(insight)
        
        return ApiResponse(success=True, data=insight)
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/{insight_id}", response_model=ApiResponse)
async def delete_insight(insight_id: int, db: Session = Depends(get_db)):
    """Delete an insight"""
    try:
        insight = db.query(InsightModel).filter(InsightModel.id == insight_id).first()
        if not insight:
            raise HTTPException(status_code=404, detail="Insight not found")
        
        db.delete(insight)
        db.commit()
        
        return ApiResponse(success=True, message="Insight deleted successfully")
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))