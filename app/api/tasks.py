from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.database import get_db
from app.models import ExecutionLog as ExecutionLogModel, SubTask as SubTaskModel
from app.schemas import TaskExecutionRequest, ApiResponse
from app.agent.life_agent import LifeAgent

router = APIRouter(prefix="/api/tasks", tags=["tasks"])

@router.post("/execute", response_model=ApiResponse)
async def execute_task(
    request: TaskExecutionRequest,
    db: Session = Depends(get_db)
):
    """Execute a task with specified action"""
    try:
        agent = LifeAgent(db)
        result = agent.trigger_task_execution(
            task_id=request.task_id,
            action=request.action,
            feedback=request.feedback
        )
        
        return ApiResponse(success=True, data=result)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/subtask/{subtask_id}/logs", response_model=ApiResponse)
async def get_subtask_logs(
    subtask_id: int,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """Get execution logs for a subtask"""
    try:
        subtask = db.query(SubTaskModel).filter(SubTaskModel.id == subtask_id).first()
        if not subtask:
            raise HTTPException(status_code=404, detail="Subtask not found")
        
        logs = db.query(ExecutionLogModel).filter(
            ExecutionLogModel.subtask_id == subtask_id
        ).offset(skip).limit(limit).all()
        
        return ApiResponse(success=True, data=logs)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/subtask/{subtask_id}/logs", response_model=ApiResponse)
async def create_execution_log(
    subtask_id: int,
    log: dict,  # Simplified for demo
    db: Session = Depends(get_db)
):
    """Create an execution log for a subtask"""
    try:
        subtask = db.query(SubTaskModel).filter(SubTaskModel.id == subtask_id).first()
        if not subtask:
            raise HTTPException(status_code=404, detail="Subtask not found")
        
        db_log = ExecutionLogModel(
            subtask_id=subtask_id,
            status=log.get("status", "TRIGGERED"),
            failure_reason_tag=log.get("failure_reason_tag"),
            user_feedback=log.get("user_feedback")
        )
        db.add(db_log)
        db.commit()
        db.refresh(db_log)
        
        return ApiResponse(success=True, data=db_log)
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))