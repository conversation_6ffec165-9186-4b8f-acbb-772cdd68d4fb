from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import func

from app.database import get_db, init_db
from app.models import Goal as GoalModel, SubTask as SubTaskModel, ExecutionLog as ExecutionLogModel
from app.schemas import ApiResponse
from app.agent.life_agent import LifeAgent

router = APIRouter(prefix="/api", tags=["system"])

@router.get("/status", response_model=ApiResponse)
async def get_system_status(db: Session = Depends(get_db)):
    """Get overall system status and statistics"""
    try:
        # Count statistics
        total_goals = db.query(func.count(GoalModel.id)).scalar()
        active_goals = db.query(func.count(GoalModel.id)).filter(GoalModel.status == "ACTIVE").scalar()
        completed_goals = db.query(func.count(GoalModel.id)).filter(GoalModel.status == "COMPLETED").scalar()
        
        total_subtasks = db.query(func.count(SubTaskModel.id)).scalar()
        active_subtasks = db.query(func.count(SubTaskModel.id)).filter(SubTaskModel.status == "ACTIVE").scalar()
        
        # Execution statistics
        total_logs = db.query(func.count(ExecutionLogModel.id)).scalar()
        completed_logs = db.query(func.count(ExecutionLogModel.id)).filter(ExecutionLogModel.status == "COMPLETED").scalar()
        failed_logs = db.query(func.count(ExecutionLogModel.id)).filter(ExecutionLogModel.status == "FAILED").scalar()
        
        success_rate = (completed_logs / total_logs * 100) if total_logs > 0 else 0
        
        status = {
            "goals": {
                "total": total_goals,
                "active": active_goals,
                "completed": completed_goals
            },
            "subtasks": {
                "total": total_subtasks,
                "active": active_subtasks
            },
            "executions": {
                "total": total_logs,
                "completed": completed_logs,
                "failed": failed_logs,
                "success_rate": round(success_rate, 2)
            },
            "system": {
                "status": "healthy",
                "timestamp": "2024-01-01T00:00:00Z"  # Will be updated dynamically
            }
        }
        
        return ApiResponse(success=True, data=status)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/reflect", response_model=ApiResponse)
async def run_reflection(db: Session = Depends(get_db)):
    """Trigger reflection and plan adjustment"""
    try:
        agent = LifeAgent(db)
        result = await agent.run_workflow()
        
        return ApiResponse(
            success=True,
            data=result,
            message="Reflection completed successfully"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/init")
async def initialize_system():
    """Initialize database and system"""
    try:
        from app.database import init_db
        await init_db()
        return {"success": True, "message": "System initialized successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/clear-all")
async def clear_all_data(db: Session = Depends(get_db)):
    """Clear all data (development/testing only)"""
    try:
        # Delete in reverse order to respect foreign keys
        db.query(ExecutionLogModel).delete()
        db.query(SubTaskModel).delete()
        db.query(GoalModel).delete()
        db.commit()
        
        return ApiResponse(
            success=True,
            message="All data cleared successfully"
        )
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "life-agent-api"}