from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from fastapi.encoders import jsonable_encoder

from app.database import get_db
from app.models import Goal as GoalModel, SubTask as SubTaskModel, ExecutionLog as ExecutionLogModel
from app.schemas import Goal, GoalCreate, GoalUpdate, SubTask, SubTaskCreate, SubTaskUpdate, ApiResponse
from app.agent.life_agent import LifeAgent

router = APIRouter(prefix="/api/goals", tags=["goals"])

@router.get("", response_model=ApiResponse)
async def get_goals(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    status: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Get all goals with optional filtering"""
    try:
        query = db.query(GoalModel)
        
        if status:
            query = query.filter(GoalModel.status == status)
        
        goals = query.offset(skip).limit(limit).all()
        
        # Load subtasks for each goal
        for goal in goals:
            goal.subtasks = db.query(SubTaskModel).filter(
                SubTaskModel.goal_id == goal.id
            ).all()
        
        return ApiResponse(success=True, data=jsonable_encoder(goals))
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("", response_model=ApiResponse)
async def create_goal(
    goal: GoalCreate,
    db: Session = Depends(get_db)
):
    """Create a new goal"""
    try:
        db_goal = GoalModel(
            name=goal.name,
            description=goal.description,
            status=goal.status
        )
        db.add(db_goal)
        db.commit()
        db.refresh(db_goal)
        
        # Trigger agent planning
        agent = LifeAgent(db)
        await agent.run_workflow()
        
        return ApiResponse(success=True, data=jsonable_encoder(db_goal))
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{goal_id}", response_model=ApiResponse)
async def get_goal(goal_id: int, db: Session = Depends(get_db)):
    """Get a specific goal by ID"""
    try:
        goal = db.query(GoalModel).filter(GoalModel.id == goal_id).first()
        if not goal:
            raise HTTPException(status_code=404, detail="Goal not found")
        
        # Load subtasks
        goal.subtasks = db.query(SubTaskModel).filter(
            SubTaskModel.goal_id == goal_id
        ).all()
        
        return ApiResponse(success=True, data=jsonable_encoder(goal))
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/{goal_id}", response_model=ApiResponse)
async def update_goal(
    goal_id: int,
    goal_update: GoalUpdate,
    db: Session = Depends(get_db)
):
    """Update a goal"""
    try:
        db_goal = db.query(GoalModel).filter(GoalModel.id == goal_id).first()
        if not db_goal:
            raise HTTPException(status_code=404, detail="Goal not found")
        
        for field, value in goal_update.model_dump(exclude_unset=True).items():
            setattr(db_goal, field, value)
        
        db.commit()
        db.refresh(db_goal)
        
        return ApiResponse(success=True, data=jsonable_encoder(db_goal))
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/{goal_id}", response_model=ApiResponse)
async def delete_goal(goal_id: int, db: Session = Depends(get_db)):
    """Delete a goal"""
    try:
        db_goal = db.query(GoalModel).filter(GoalModel.id == goal_id).first()
        if not db_goal:
            raise HTTPException(status_code=404, detail="Goal not found")
        
        db.delete(db_goal)
        db.commit()
        
        return ApiResponse(success=True, message="Goal deleted successfully")
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{goal_id}/subtasks", response_model=ApiResponse)
async def get_goal_subtasks(goal_id: int, db: Session = Depends(get_db)):
    """Get all subtasks for a specific goal"""
    try:
        goal = db.query(GoalModel).filter(GoalModel.id == goal_id).first()
        if not goal:
            raise HTTPException(status_code=404, detail="Goal not found")
        
        subtasks = db.query(SubTaskModel).filter(
            SubTaskModel.goal_id == goal_id
        ).all()
        
        # Load execution logs for each subtask
        for subtask in subtasks:
            subtask.execution_logs = db.query(ExecutionLogModel).filter(
                ExecutionLogModel.subtask_id == subtask.id
            ).all()
        
        return ApiResponse(success=True, data=jsonable_encoder(subtasks))
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{goal_id}/subtasks", response_model=ApiResponse)
async def create_subtask(
    goal_id: int,
    subtask: SubTaskCreate,
    db: Session = Depends(get_db)
):
    """Create a new subtask for a goal"""
    try:
        goal = db.query(GoalModel).filter(GoalModel.id == goal_id).first()
        if not goal:
            raise HTTPException(status_code=404, detail="Goal not found")
        
        db_subtask = SubTaskModel(
            goal_id=goal_id,
            name=subtask.name,
            description=subtask.description,
            trigger_frequency=subtask.trigger_frequency,
            trigger_time=subtask.trigger_time,
            estimated_duration=subtask.estimated_duration,
            status=subtask.status
        )
        db.add(db_subtask)
        db.commit()
        db.refresh(db_subtask)
        
        return ApiResponse(success=True, data=jsonable_encoder(db_subtask))
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))