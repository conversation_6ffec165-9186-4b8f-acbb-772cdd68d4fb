from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from app.models import GoalStatus, TaskStatus, ExecutionStatus, InsightType

class GoalBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    status: GoalStatus = GoalStatus.ACTIVE

class GoalCreate(GoalBase):
    pass

class GoalUpdate(GoalBase):
    pass

class Goal(GoalBase):
    id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    subtasks: List["SubTask"] = []

    class Config:
        from_attributes = True

class SubTaskBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    trigger_frequency: str = Field(..., pattern=r"^(daily|weekly|biweekly|monthly|custom)$")
    trigger_time: str = Field(..., pattern=r"^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$")
    estimated_duration: int = Field(..., ge=1, le=480)
    status: TaskStatus = TaskStatus.ACTIVE

class SubTaskCreate(SubTaskBase):
    goal_id: int

class SubTaskUpdate(SubTaskBase):
    pass

class SubTask(SubTaskBase):
    id: Optional[int] = None
    goal_id: int
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    execution_logs: List["ExecutionLog"] = []

    class Config:
        from_attributes = True

class ExecutionLogBase(BaseModel):
    subtask_id: int
    status: ExecutionStatus
    failure_reason_tag: Optional[str] = None
    user_feedback: Optional[str] = None

class ExecutionLogCreate(ExecutionLogBase):
    pass

class ExecutionLog(ExecutionLogBase):
    id: Optional[int] = None
    triggered_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class InsightBase(BaseModel):
    type: InsightType
    content: str = Field(..., min_length=1)
    data: Optional[Dict[str, Any]] = None

class InsightCreate(InsightBase):
    pass

class Insight(InsightBase):
    id: Optional[int] = None
    created_at: Optional[datetime] = None
    applied: bool = False

    class Config:
        from_attributes = True

class ApiResponse(BaseModel):
    success: bool
    data: Optional[Any] = None
    message: Optional[str] = None

class TaskExecutionRequest(BaseModel):
    task_id: int
    action: str = Field(..., pattern=r"^(start|complete|fail|defer|ignore)$")
    feedback: Optional[str] = None

class ReflectionResult(BaseModel):
    success: bool
    analysis: str
    recommendations: List[str]

# Update forward references
Goal.model_rebuild()
SubTask.model_rebuild()