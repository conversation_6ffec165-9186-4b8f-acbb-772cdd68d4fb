import os
from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # Database
    database_url: str = "postgresql://lifeagent:lifeagent123@localhost:5432/lifeagent_db"
    
    # Redis
    redis_url: str = "redis://localhost:6379"
    redis_password: Optional[str] = None
    
    # OpenAI
    openai_api_key: str = "your-openai-api-key-here"
    openai_base_url: str = "https://api.openai.com/v1"
    model: str = "gpt-4o-mini"
    temperature: float = 0.7
    max_tokens: int = 1000
    
    # Server
    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = True
    log_level: str = "INFO"
    
    # CORS
    cors_origins: list = ["http://localhost:3000", "http://localhost:8080", "http://********:3000"]
    
    class Config:
        env_file = ".env."+os.environ.get("ENV", "local")
        case_sensitive = False

settings = Settings()