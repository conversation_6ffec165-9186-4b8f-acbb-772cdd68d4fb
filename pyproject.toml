[project]
name = "back-end"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "alembic>=1.16.4",
    "asyncpg>=0.30.0",
    "fastapi>=0.116.1",
    "langchain>=0.3.27",
    "langchain-openai>=0.3.28",
    "langgraph>=0.5.4",
    "psycopg2-binary>=2.9.10",
    "pydantic>=2.11.7",
    "pydantic-settings>=2.10.1",
    "python-dotenv>=1.1.1",
    "python-multipart>=0.0.20",
    "redis>=6.2.0",
    "sqlalchemy>=2.0.41",
    "uvicorn>=0.35.0",
]

[[tool.uv.index]]
url = "https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple"
default = true
